import React, { useEffect, useReducer, useCallback, useRef } from 'react';
import styled from 'styled-components';
import { User, Search, ChevronDown, ChevronUp } from 'lucide-react';
import { appTheme } from '@/app/theme';
import { feedbackUserSearchApi } from '@/services/feedbackService';
import useUserStore from '@/store/userStore';

// Types
interface FeedbackUser {
  id: number;
  name: string;
  email: string;
  imageUrl?: string;
  departmentName?: string;
  organizationName?: string;
  isLeader?: boolean;
  isAdmin?: boolean;
  isOwner?: boolean;
}

interface Organization {
  id: number;
  name: string;
}

interface Department {
  id: number;
  name: string;
  description?: string | null;
  memberCount: number;
  userJoinedAt: string;
  isDirectMember: boolean;
  isLeader: boolean;
}

interface DynamicRecipientsFieldProps {
  feedbackTypeId: string;
  selectedUserIds: number[];
  onUserIdsChange: (userIds: number[]) => void;
  feedbackTypes: any[];
}

// New state management types
interface RecipientsState {
  recipients: FeedbackUser[];
  search: {
    isOpen: boolean;
    isLoading: boolean;
    results: FeedbackUser[];
    selectedIds: Set<number>;
    filters: {
      organizationId: string;
      departmentId: string;
      name: string;
    };
    organizations: Organization[];
    departments: Department[];
    searchTimeout: NodeJS.Timeout | null;
  };
}

type RecipientsAction =
  | { type: 'SET_RECIPIENTS'; payload: FeedbackUser[] }
  | { type: 'ADD_RECIPIENTS'; payload: FeedbackUser[] }
  | { type: 'REMOVE_RECIPIENT'; payload: number }
  | { type: 'CLEAR_RECIPIENTS' }
  | { type: 'TOGGLE_SEARCH'; payload?: boolean }
  | { type: 'SET_SEARCH_LOADING'; payload: boolean }
  | { type: 'SET_SEARCH_RESULTS'; payload: FeedbackUser[] }
  | { type: 'TOGGLE_SEARCH_USER'; payload: number }
  | { type: 'CLEAR_SEARCH_SELECTION' }
  | { type: 'UPDATE_SEARCH_FILTERS'; payload: Partial<RecipientsState['search']['filters']> }
  | { type: 'SET_SEARCH_ORGANIZATIONS'; payload: Organization[] }
  | { type: 'SET_SEARCH_DEPARTMENTS'; payload: Department[] }
  | { type: 'SET_SEARCH_TIMEOUT'; payload: NodeJS.Timeout | null }
  | { type: 'RESET_SEARCH_STATE' };

// Styled Components
const RecipientsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${appTheme.spacing.md};
`;

const StaticUserSelection = styled.div`
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  padding: ${appTheme.spacing.md};
  background: ${appTheme.colors.background.light};
`;

const UserSelectionHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
  margin-bottom: ${appTheme.spacing.md};
  font-weight: ${appTheme.typography.fontWeights.medium};
  color: ${appTheme.colors.text.primary};
`;

const UserList = styled.div`
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  background: ${appTheme.colors.background.main};
`;

const UserItem = styled.div<{ $selected: boolean }>`
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  border-bottom: 1px solid ${appTheme.colors.border};
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
  background: ${props => (props.$selected ? appTheme.colors.primaryLight : 'transparent')};
  color: ${props => (props.$selected ? appTheme.colors.primary : appTheme.colors.text.primary)};
  transition: ${appTheme.transitions.default};

  &:hover {
    background: ${props =>
      props.$selected ? appTheme.colors.primaryLight : appTheme.colors.background.lighter};
  }

  &:last-child {
    border-bottom: none;
  }
`;

const DynamicUserSelection = styled.div`
  border: 1px solid rgba(209, 213, 219, 0.6);
  border-radius: ${appTheme.borderRadius.lg};
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
  backdrop-filter: blur(10px);
  min-height: 80px;
  padding: ${appTheme.spacing.md};
  display: flex;
  flex-direction: column;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
`;

const DynamicUserHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${appTheme.spacing.sm};
  border-bottom: 1px solid rgba(229, 231, 235, 0.6);
  background: linear-gradient(135deg, rgba(249, 250, 251, 0.8), rgba(243, 244, 246, 0.6));
  backdrop-filter: blur(5px);
  border-radius: ${appTheme.borderRadius.lg} ${appTheme.borderRadius.lg} 0 0;
  margin: -${appTheme.spacing.md} -${appTheme.spacing.md}
    ${appTheme.spacing.md} -${appTheme.spacing.md};
`;

const DynamicUserTitle = styled.span`
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  color: ${appTheme.colors.text.primary};
`;

const AddUserButton = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border-radius: ${appTheme.borderRadius.md};
  cursor: pointer;
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  transition: all 0.2s ease;
  border: none;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);

  &:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
  }

  &:active {
    transform: translateY(0);
  }
`;

const SelectedUsersList = styled.div`
  padding: ${appTheme.spacing.sm};
  overflow-y: auto;
  flex: 1;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(243, 244, 246, 0.5);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.7);
    border-radius: 3px;

    &:hover {
      background: rgba(156, 163, 175, 0.7);
    }
  }
`;

const SelectedUserItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${appTheme.spacing.sm};
  border-radius: ${appTheme.borderRadius.md};
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.6));
  backdrop-filter: blur(5px);
  border: 1px solid rgba(229, 231, 235, 0.6);
  margin-bottom: ${appTheme.spacing.sm};
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  &:hover {
    background: linear-gradient(135deg, rgba(249, 250, 251, 0.9), rgba(243, 244, 246, 0.7));
    border-color: rgba(209, 213, 219, 0.8);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }

  &:last-child {
    margin-bottom: 0;
  }
`;

const UserAvatar = styled.div<{ $imageUrl?: string; $size?: 'small' | 'medium' }>`
  width: ${props => (props.$size === 'small' ? '32px' : '36px')};
  height: ${props => (props.$size === 'small' ? '32px' : '36px')};
  border-radius: 50%;
  background: ${props =>
    props.$imageUrl ? `url(${props.$imageUrl})` : 'linear-gradient(135deg, #6366f1, #8b5cf6)'};
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: ${appTheme.typography.fontWeights.semibold};
  font-size: ${props => (props.$size === 'small' ? '12px' : '14px')};
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
`;

const UserInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
`;

const UserName = styled.span`
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: #1f2937;
  letter-spacing: -0.025em;
`;

const UserEmail = styled.span`
  font-size: ${appTheme.typography.fontSizes.xs};
  color: #6b7280;
  font-weight: 400;
`;

const RemoveUserButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);

  &:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(239, 68, 68, 0.4);
  }

  &:active {
    transform: scale(0.95);
  }
`;

const EmptyUserList = styled.div`
  text-align: center;
  padding: ${appTheme.spacing.md};
  color: #9ca3af;
  font-size: ${appTheme.typography.fontSizes.xs};
  font-style: italic;
  background: linear-gradient(135deg, rgba(249, 250, 251, 0.6), rgba(243, 244, 246, 0.4));
  border-radius: ${appTheme.borderRadius.md};
  border: 1px dashed rgba(209, 213, 219, 0.6);
`;

// Collapse styled components
const SearchCollapseContainer = styled.div<{ $isOpen: boolean }>`
  margin-top: ${appTheme.spacing.md};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  background: white;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  max-height: ${props => props.$isOpen ? '800px' : '0'};
  opacity: ${props => props.$isOpen ? '1' : '0'};
  transform: ${props => props.$isOpen ? 'translateY(0)' : 'translateY(-10px)'};
  box-shadow: ${props => props.$isOpen ? '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)' : 'none'};
`;

const SearchCollapseContent = styled.div`
  padding: ${appTheme.spacing.md};
`;

const SearchCollapseHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: ${appTheme.spacing.sm};
  border-bottom: 1px solid ${appTheme.colors.border};
  margin-bottom: ${appTheme.spacing.md};
`;

const SearchCollapseTitle = styled.h4`
  margin: 0;
  font-size: ${appTheme.typography.fontSizes.base};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: ${appTheme.colors.text.primary};
`;

const AddSelectedButton = styled.button`
  background: ${appTheme.colors.primary};
  color: white;
  border: none;
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  border-radius: ${appTheme.borderRadius.md};
  font-weight: ${appTheme.typography.fontWeights.medium};
  cursor: pointer;
  transition: ${appTheme.transitions.default};
  margin-top: ${appTheme.spacing.md};
  width: 100%;

  &:hover {
    background: ${appTheme.colors.primaryHover};
  }

  &:disabled {
    background: #d1d5db;
    cursor: not-allowed;
  }
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${appTheme.spacing.sm};
`;

const FormLabel = styled.label`
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  color: ${appTheme.colors.text.primary};
`;

const FormSelect = styled.select`
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.sm};
  background: ${appTheme.colors.background.main};
  color: ${appTheme.colors.text.primary};
  transition: ${appTheme.transitions.default};

  &:focus {
    outline: none;
    border-color: ${appTheme.colors.primary};
    box-shadow: 0 0 0 2px ${appTheme.colors.primaryLight};
  }

  &:disabled {
    background: ${appTheme.colors.background.lighter};
    color: ${appTheme.colors.text.secondary};
    cursor: not-allowed;
  }
`;

const FormInput = styled.input`
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.sm};
  background: ${appTheme.colors.background.main};
  color: ${appTheme.colors.text.primary};
  transition: ${appTheme.transitions.default};

  &:focus {
    outline: none;
    border-color: ${appTheme.colors.primary};
    box-shadow: 0 0 0 2px ${appTheme.colors.primaryLight};
  }

  &::placeholder {
    color: ${appTheme.colors.text.secondary};
  }
`;

const CustomCheckbox = styled.input.attrs({ type: 'checkbox' })`
  width: 16px;
  height: 16px;
  accent-color: ${appTheme.colors.primary};
  cursor: pointer;
`;

// Mock users for non-private feedback types
const mockUsers = [
  { id: 1, firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
  { id: 2, firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>' },
  { id: 3, firstName: 'Bob', lastName: 'Johnson', email: '<EMAIL>' },
];

// Helper function to get user initials
const getUserInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');
};

// Initial state for the new state management
const initialRecipientsState: RecipientsState = {
  recipients: [],
  search: {
    isOpen: false,
    isLoading: false,
    results: [],
    selectedIds: new Set(),
    filters: {
      organizationId: '',
      departmentId: '',
      name: '',
    },
    organizations: [],
    departments: [],
    searchTimeout: null,
  },
};

// Reducer for managing recipients state
const recipientsReducer = (state: RecipientsState, action: RecipientsAction): RecipientsState => {
  switch (action.type) {
    case 'SET_RECIPIENTS':
      return {
        ...state,
        recipients: action.payload,
      };

    case 'ADD_RECIPIENTS':
      console.log('REDUCER: ADD_RECIPIENTS action received with payload:', action.payload);
      console.log('REDUCER: Current recipients before add:', state.recipients);
      // Filter out duplicates
      const newRecipients = action.payload.filter(
        newUser => !state.recipients.find(existing => existing.id === newUser.id)
      );
      console.log('REDUCER: New recipients to add (after duplicate filter):', newRecipients);
      const updatedRecipients = [...state.recipients, ...newRecipients];
      console.log('REDUCER: Updated recipients list:', updatedRecipients);
      return {
        ...state,
        recipients: updatedRecipients,
      };

    case 'REMOVE_RECIPIENT':
      return {
        ...state,
        recipients: state.recipients.filter(user => user.id !== action.payload),
      };

    case 'CLEAR_RECIPIENTS':
      return {
        ...state,
        recipients: [],
      };

    case 'TOGGLE_SEARCH':
      return {
        ...state,
        search: {
          ...state.search,
          isOpen: action.payload !== undefined ? action.payload : !state.search.isOpen,
        },
      };

    case 'SET_SEARCH_LOADING':
      return {
        ...state,
        search: {
          ...state.search,
          isLoading: action.payload,
        },
      };

    case 'SET_SEARCH_RESULTS':
      return {
        ...state,
        search: {
          ...state.search,
          results: action.payload,
        },
      };

    case 'TOGGLE_SEARCH_USER':
      console.log('REDUCER: TOGGLE_SEARCH_USER action received for user ID:', action.payload);
      console.log('REDUCER: Current selectedIds:', state.search.selectedIds);
      const newSelectedIds = new Set(state.search.selectedIds);
      if (newSelectedIds.has(action.payload)) {
        console.log('REDUCER: Removing user from selection');
        newSelectedIds.delete(action.payload);
      } else {
        console.log('REDUCER: Adding user to selection');
        newSelectedIds.add(action.payload);
      }
      console.log('REDUCER: New selectedIds:', newSelectedIds);
      return {
        ...state,
        search: {
          ...state.search,
          selectedIds: newSelectedIds,
        },
      };

    case 'CLEAR_SEARCH_SELECTION':
      console.log('REDUCER: CLEAR_SEARCH_SELECTION action received');
      console.log('REDUCER: Clearing selectedIds from:', state.search.selectedIds);
      return {
        ...state,
        search: {
          ...state.search,
          selectedIds: new Set(),
        },
      };

    case 'UPDATE_SEARCH_FILTERS':
      return {
        ...state,
        search: {
          ...state.search,
          filters: {
            ...state.search.filters,
            ...action.payload,
          },
        },
      };

    case 'SET_SEARCH_ORGANIZATIONS':
      return {
        ...state,
        search: {
          ...state.search,
          organizations: action.payload,
        },
      };

    case 'SET_SEARCH_DEPARTMENTS':
      return {
        ...state,
        search: {
          ...state.search,
          departments: action.payload,
        },
      };

    case 'SET_SEARCH_TIMEOUT':
      // Clear existing timeout if any
      if (state.search.searchTimeout) {
        clearTimeout(state.search.searchTimeout);
      }
      return {
        ...state,
        search: {
          ...state.search,
          searchTimeout: action.payload,
        },
      };

    case 'RESET_SEARCH_STATE':
      // Clear timeout if exists
      if (state.search.searchTimeout) {
        clearTimeout(state.search.searchTimeout);
      }
      return {
        ...state,
        search: {
          ...initialRecipientsState.search,
        },
      };

    default:
      return state;
  }
};

export default function DynamicRecipientsField({
  feedbackTypeId,
  selectedUserIds,
  onUserIdsChange,
  feedbackTypes,
}: DynamicRecipientsFieldProps) {
  const { userData } = useUserStore();

  // New state management using useReducer
  const [recipientsState, dispatch] = useReducer(recipientsReducer, initialRecipientsState);
  const isAddingUsersRef = useRef(false);

  // Get the selected feedback type with more robust matching
  const selectedFeedbackType = feedbackTypes.find(type =>
    type.id.toString() === feedbackTypeId || type.id === parseInt(feedbackTypeId)
  );
  const isPrivateFeedback = selectedFeedbackType?.name === 'private' || selectedFeedbackType?.id === 1;

  // Handle user toggle for static (non-private) feedback types
  const handleUserToggle = (userId: number) => {
    const newUserIds = selectedUserIds.includes(userId)
      ? selectedUserIds.filter(id => id !== userId)
      : [...selectedUserIds, userId];
    onUserIdsChange(newUserIds);
  };

  // Remove user from selected list (for private feedback)
  const removeUserFromList = useCallback((userId: number) => {
    dispatch({ type: 'REMOVE_RECIPIENT', payload: userId });
    const updatedUserIds = selectedUserIds.filter(id => id !== userId);
    onUserIdsChange(updatedUserIds);
  }, [selectedUserIds, onUserIdsChange]);

  // Search for users
  const searchUsers = useCallback(async (filters = recipientsState.search.filters) => {
    if (!filters.organizationId && !filters.departmentId && !filters.name.trim()) {
      dispatch({ type: 'SET_SEARCH_RESULTS', payload: [] });
      return;
    }

    dispatch({ type: 'SET_SEARCH_LOADING', payload: true });
    try {
      const response = await feedbackUserSearchApi.searchUsers(filters);

      if (response?.members) {
        // Transform API response to match our FeedbackUser interface
        const transformedMembers: FeedbackUser[] = response.members.map((member: any) => ({
          id: member.user.id,
          name: `${member.user.firstName} ${member.user.lastName}`,
          email: member.user.email,
          imageUrl: member.user.imageUrl,
          departmentName: member.department?.name,
          organizationName: member.department?.organization?.name,
          isLeader: member.isLeader,
          isAdmin: member.isAdmin,
          isOwner: member.isOwner,
        }));

        dispatch({ type: 'SET_SEARCH_RESULTS', payload: transformedMembers });
      } else {
        dispatch({ type: 'SET_SEARCH_RESULTS', payload: [] });
      }
    } catch (err) {
      console.error('Error searching users:', err);
      dispatch({ type: 'SET_SEARCH_RESULTS', payload: [] });
    } finally {
      dispatch({ type: 'SET_SEARCH_LOADING', payload: false });
    }
  }, [recipientsState.search.filters]);

  // Debounced search function
  const debouncedSearch = useCallback((filters: RecipientsState['search']['filters']) => {
    if (recipientsState.search.searchTimeout) {
      clearTimeout(recipientsState.search.searchTimeout);
    }

    const timeout = setTimeout(() => {
      searchUsers(filters);
    }, 500); // 500ms debounce

    dispatch({ type: 'SET_SEARCH_TIMEOUT', payload: timeout });
  }, [recipientsState.search.searchTimeout, searchUsers]);

  // Handle search filter changes
  const handleSearchFilterChange = useCallback((field: string, value: string) => {
    const newFilters = { ...recipientsState.search.filters, [field]: value };
    dispatch({ type: 'UPDATE_SEARCH_FILTERS', payload: { [field]: value } });

    // Reset departments when organization changes
    if (field === 'organizationId') {
      dispatch({ type: 'UPDATE_SEARCH_FILTERS', payload: { departmentId: '' } });
      dispatch({ type: 'SET_SEARCH_DEPARTMENTS', payload: [] });

      // Load departments for the selected organization
      if (value && userData?.organizations) {
        const selectedOrg = userData.organizations.find(org => org.id.toString() === value);
        if (selectedOrg?.departments) {
          dispatch({ type: 'SET_SEARCH_DEPARTMENTS', payload: selectedOrg.departments });
        }
      }
    }

    // Trigger debounced search if we have search criteria
    if (newFilters.organizationId || newFilters.departmentId || newFilters.name.trim()) {
      debouncedSearch(newFilters);
    } else {
      dispatch({ type: 'SET_SEARCH_RESULTS', payload: [] });
    }
  }, [recipientsState.search.filters, userData?.organizations, debouncedSearch]);

  // Toggle user selection in modal
  const toggleUserSelection = useCallback((user: FeedbackUser) => {
    dispatch({ type: 'TOGGLE_SEARCH_USER', payload: user.id });
  }, []);

  // Add selected users to assigned list
  const addSelectedUsersToList = useCallback(() => {
    console.log('=== addSelectedUsersToList START ===');
    console.log('Before - recipients:', recipientsState.recipients);
    console.log('Before - selectedUserIds:', selectedUserIds);
    console.log('modalSelectedUsers:', recipientsState.search.selectedIds);
    console.log('search results:', recipientsState.search.results);

    // Validate that we have selected users
    if (recipientsState.search.selectedIds.size === 0) {
      console.log('No users selected, aborting');
      return;
    }

    // Validate that we have search results
    if (recipientsState.search.results.length === 0) {
      console.log('No search results available, aborting');
      return;
    }

    const usersToAdd = recipientsState.search.results.filter(
      user => recipientsState.search.selectedIds.has(user.id) &&
               !recipientsState.recipients.find(u => u.id === user.id)
    );

    console.log('usersToAdd:', usersToAdd);
    console.log('usersToAdd length:', usersToAdd.length);

    if (usersToAdd.length > 0) {
      const newUserIds = [...selectedUserIds, ...usersToAdd.map(u => u.id)];

      console.log('Setting newUserIds:', newUserIds);
      console.log('Dispatching ADD_RECIPIENTS with payload:', usersToAdd);

      // Set flag to prevent useEffect from clearing recipients
      isAddingUsersRef.current = true;

      // First update the local state
      console.log('About to dispatch ADD_RECIPIENTS...');
      dispatch({ type: 'ADD_RECIPIENTS', payload: usersToAdd });
      console.log('ADD_RECIPIENTS dispatched');

      // Then update the parent state
      console.log('Calling onUserIdsChange with:', newUserIds);
      onUserIdsChange(newUserIds);

      // Finally clear the selection
      console.log('Dispatching CLEAR_SEARCH_SELECTION');
      dispatch({ type: 'CLEAR_SEARCH_SELECTION' });
      console.log('CLEAR_SEARCH_SELECTION dispatched');

      // Reset flag after a short delay to allow state updates to complete
      setTimeout(() => {
        isAddingUsersRef.current = false;
        console.log('Reset isAddingUsersRef flag');
      }, 200);

      console.log('=== addSelectedUsersToList END ===');
    } else {
      console.log('No users to add - either none selected or all already in recipients');
      console.log('Selected IDs:', Array.from(recipientsState.search.selectedIds));
      console.log('Current recipients IDs:', recipientsState.recipients.map(u => u.id));
    }
  }, [recipientsState.recipients, recipientsState.search.results, recipientsState.search.selectedIds, selectedUserIds, onUserIdsChange]);

  // Toggle user search collapse
  const toggleCollapse = useCallback(() => {
    if (!recipientsState.search.isOpen) {
      // Initialize organizations from userData when opening
      if (userData?.organizations && userData.organizations.length > 0) {
        const orgs = userData.organizations.map(org => ({
          id: org.id,
          name: org.name,
        }));
        dispatch({ type: 'SET_SEARCH_ORGANIZATIONS', payload: orgs });

        // Auto-select first organization and trigger search
        const firstOrg = orgs[0];
        const initialFilters = {
          organizationId: firstOrg.id.toString(),
          departmentId: '',
          name: '',
        };
        dispatch({ type: 'UPDATE_SEARCH_FILTERS', payload: initialFilters });

        // Load departments for first organization
        if (userData.organizations[0]?.departments) {
          dispatch({ type: 'SET_SEARCH_DEPARTMENTS', payload: userData.organizations[0].departments });
        }

        // Trigger initial search
        searchUsers(initialFilters);
      } else {
        // Fallback: open collapse without pre-populated data
        console.warn('No organization data available, opening empty collapse');
        dispatch({ type: 'SET_SEARCH_ORGANIZATIONS', payload: [] });
        dispatch({ type: 'SET_SEARCH_DEPARTMENTS', payload: [] });
        dispatch({ type: 'UPDATE_SEARCH_FILTERS', payload: { organizationId: '', departmentId: '', name: '' } });
      }
    }

    dispatch({ type: 'TOGGLE_SEARCH' });
  }, [recipientsState.search.isOpen, userData?.organizations, searchUsers]);

  // Synchronize recipients state with selectedUserIds prop
  // This useEffect should only handle external changes to selectedUserIds
  // and avoid interfering with internal add/remove operations
  useEffect(() => {
    console.log('=== useEffect SYNC ===');
    console.log('selectedUserIds prop:', selectedUserIds);
    console.log('recipients state length:', recipientsState.recipients.length);
    console.log('isAddingUsersRef.current:', isAddingUsersRef.current);

    // Don't interfere if we're currently adding users
    if (isAddingUsersRef.current) {
      console.log('Skipping sync because we are adding users');
      return;
    }

    // Only clear recipients if selectedUserIds is empty and we have recipients
    // This handles the case where the parent component resets the form
    if (selectedUserIds.length === 0 && recipientsState.recipients.length > 0) {
      console.log('Clearing recipients because selectedUserIds is empty (external reset)');
      dispatch({ type: 'CLEAR_RECIPIENTS' });
    }

    // For all other cases, let the internal add/remove functions handle the state
    console.log('useEffect sync completed');
  }, [selectedUserIds.length, recipientsState.recipients.length]);

  // Reset when feedback type changes
  // TEMPORARILY DISABLED - Testing if this useEffect is causing recipients to disappear
  /*
  useEffect(() => {
    console.log('=== feedbackTypeId useEffect ===');
    console.log('feedbackTypeId:', feedbackTypeId);
    console.log('selectedUserIds.length:', selectedUserIds.length);
    console.log('recipients.length:', recipientsState.recipients.length);
    console.log('isAddingUsersRef.current:', isAddingUsersRef.current);

    // Don't clear if we're currently adding users
    if (isAddingUsersRef.current) {
      console.log('Skipping clear because we are adding users');
      return;
    }

    // Only clear if we actually have recipients to clear
    // This prevents clearing when we're just adding users
    if (feedbackTypeId && recipientsState.recipients.length > 0) {
      console.log('Clearing recipients due to feedback type change');
      dispatch({ type: 'CLEAR_RECIPIENTS' });
      onUserIdsChange([]);
    }
  }, [feedbackTypeId, onUserIdsChange, recipientsState.recipients.length]);
  */

  if (!feedbackTypeId) {
    return (
      <RecipientsContainer>
        <UserSelectionHeader>
          <User size={16} />
          Please select a feedback type first
        </UserSelectionHeader>
        <div style={{
          fontSize: '12px',
          color: '#6b7280',
          marginTop: '8px',
          padding: '8px',
          backgroundColor: '#f9fafb',
          borderRadius: '4px',
          border: '1px solid #e5e7eb'
        }}>
          💡 <strong>Tip:</strong> Select "Private Feedback" to use the advanced user search feature
        </div>
      </RecipientsContainer>
    );
  }

  if (!isPrivateFeedback) {
    // Render static user selection for non-private feedback types
    return (
      <RecipientsContainer>
        <StaticUserSelection>
          <UserSelectionHeader>
            <User size={16} />
            Select users to receive this feedback
          </UserSelectionHeader>
          <UserList>
            {mockUsers.map(user => (
              <UserItem
                key={user.id}
                $selected={selectedUserIds.includes(user.id)}
                onClick={() => handleUserToggle(user.id)}
              >
                <User size={16} />
                {user.firstName} {user.lastName} ({user.email})
              </UserItem>
            ))}
          </UserList>
        </StaticUserSelection>
      </RecipientsContainer>
    );
  }

  // Debug: Log current state on every render
  console.log('=== COMPONENT RENDER ===');
  console.log('selectedUserIds prop:', selectedUserIds);
  console.log('recipientsState.recipients:', recipientsState.recipients);
  console.log('recipientsState.recipients.length:', recipientsState.recipients.length);

  // Render dynamic user selection for private feedback
  return (
    <RecipientsContainer>
      <DynamicUserSelection>
        <DynamicUserHeader>
          <DynamicUserTitle>Selected Recipients ({recipientsState.recipients.length})</DynamicUserTitle>
          <AddUserButton onClick={toggleCollapse}>
            {recipientsState.search.isOpen ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            {recipientsState.search.isOpen ? 'Hide Search' : 'Find Users'}
          </AddUserButton>
        </DynamicUserHeader>
        <SelectedUsersList>
          {recipientsState.recipients.length === 0 ? (
            <EmptyUserList>
              No recipients selected. Click "Find Users" to add recipients.
            </EmptyUserList>
          ) : (
            recipientsState.recipients.map(user => (
              <SelectedUserItem key={user.id}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <UserAvatar $imageUrl={user.imageUrl} $size="medium">
                    {!user.imageUrl && getUserInitials(user.name)}
                  </UserAvatar>
                  <UserInfo>
                    <UserName>{user.name}</UserName>
                    <UserEmail>{user.email}</UserEmail>
                  </UserInfo>
                </div>
                <RemoveUserButton onClick={() => removeUserFromList(user.id)}>×</RemoveUserButton>
              </SelectedUserItem>
            ))
          )}
        </SelectedUsersList>
      </DynamicUserSelection>

      {/* User Search Collapse */}
      <SearchCollapseContainer $isOpen={recipientsState.search.isOpen}>
        <SearchCollapseContent>
          <SearchCollapseHeader>
            <SearchCollapseTitle>Find Users</SearchCollapseTitle>
            {/* Temporary debug buttons */}
            <div style={{ display: 'flex', gap: '4px' }}>
              <button
                onClick={() => {
                  console.log('=== CURRENT STATE DEBUG ===');
                  console.log('recipientsState:', recipientsState);
                  console.log('selectedUserIds prop:', selectedUserIds);
                  console.log('search.selectedIds:', recipientsState.search.selectedIds);
                  console.log('search.results:', recipientsState.search.results);
                  console.log('recipients:', recipientsState.recipients);
                }}
                style={{
                  padding: '4px 8px',
                  fontSize: '10px',
                  background: '#e3f2fd',
                  border: '1px solid #2196f3',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  color: '#1976d2'
                }}
              >
                Debug State
              </button>
              <button
                onClick={() => {
                  console.log('=== MANUAL ADD TEST ===');
                  const testUser: FeedbackUser = {
                    id: 999,
                    name: 'Test User',
                    email: '<EMAIL>',
                    imageUrl: undefined
                  };
                  console.log('Adding test user:', testUser);
                  dispatch({ type: 'ADD_RECIPIENTS', payload: [testUser] });
                }}
                style={{
                  padding: '4px 8px',
                  fontSize: '10px',
                  background: '#e8f5e8',
                  border: '1px solid #4caf50',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  color: '#2e7d32'
                }}
              >
                Test Add
              </button>
            </div>
          </SearchCollapseHeader>

            <div>
              <div
                style={{
                  display: 'grid',
                  gridTemplateColumns: '1fr 1fr',
                  gap: '12px',
                  marginBottom: '12px',
                }}
              >
                <FormGroup>
                  <FormLabel>Organization</FormLabel>
                  <FormSelect
                    value={recipientsState.search.filters.organizationId}
                    onChange={e => handleSearchFilterChange('organizationId', e.target.value)}
                  >
                    <option value="">All Organizations</option>
                    {recipientsState.search.organizations.map(org => (
                      <option key={org.id} value={org.id}>
                        {org.name}
                      </option>
                    ))}
                  </FormSelect>
                </FormGroup>

                <FormGroup>
                  <FormLabel>Department</FormLabel>
                  <FormSelect
                    value={recipientsState.search.filters.departmentId}
                    onChange={e => handleSearchFilterChange('departmentId', e.target.value)}
                    disabled={!recipientsState.search.filters.organizationId}
                  >
                    <option value="">
                      {recipientsState.search.filters.organizationId
                        ? 'All Departments'
                        : 'Select organization first'}
                    </option>
                    {recipientsState.search.departments.map(dept => (
                      <option key={dept.id} value={dept.id}>
                        {dept.name}
                      </option>
                    ))}
                  </FormSelect>
                </FormGroup>

                <FormGroup style={{ gridColumn: '1 / -1' }}>
                  <FormLabel>Name</FormLabel>
                  <div style={{ position: 'relative' }}>
                    <div
                      style={{
                        position: 'absolute',
                        left: '12px',
                        top: '50%',
                        transform: 'translateY(-50%)',
                        color: '#6b7280',
                      }}
                    >
                      <Search size={16} />
                    </div>
                    <FormInput
                      type="text"
                      placeholder="Search by name or email..."
                      value={recipientsState.search.filters.name}
                      onChange={e => handleSearchFilterChange('name', e.target.value)}
                      style={{ paddingLeft: '36px' }}
                    />
                  </div>
                </FormGroup>
              </div>

              {recipientsState.search.isLoading && (
                <div style={{ textAlign: 'center', padding: '20px', color: '#6b7280' }}>
                  Searching users...
                </div>
              )}

              {!recipientsState.search.isLoading && recipientsState.search.results.length > 0 && (
                <>
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: '8px',
                      padding: '4px 8px',
                      backgroundColor: '#f8fafc',
                      borderRadius: '4px',
                    }}
                  >
                    <span style={{ fontSize: '12px', color: '#6b7280' }}>
                      {recipientsState.search.results.length} result{recipientsState.search.results.length !== 1 ? 's' : ''} found
                    </span>
                    <AddSelectedButton
                      onClick={addSelectedUsersToList}
                      disabled={recipientsState.search.selectedIds.size === 0}
                    >
                      Add Selected ({recipientsState.search.selectedIds.size})
                    </AddSelectedButton>
                  </div>

                  <div
                    style={{
                      maxHeight: '280px',
                      overflowY: 'auto',
                      border: '1px solid #e5e7eb',
                      borderRadius: '4px',
                    }}
                  >
                    {recipientsState.search.results.map(user => (
                      <div
                        key={user.id}
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          padding: '8px',
                          borderBottom: '1px solid #f3f4f6',
                        }}
                      >
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                          <CustomCheckbox
                            checked={recipientsState.search.selectedIds.has(user.id)}
                            onChange={() => toggleUserSelection(user)}
                          />
                          <UserAvatar $imageUrl={user.imageUrl} $size="small">
                            {!user.imageUrl && getUserInitials(user.name)}
                          </UserAvatar>
                          <div>
                            <div
                              style={{
                                fontWeight: 500,
                                fontSize: '14px',
                                display: 'flex',
                                alignItems: 'center',
                                gap: '6px',
                                flexWrap: 'wrap',
                              }}
                            >
                              {user.name}
                              {user.isOwner && (
                                <span
                                  style={{
                                    backgroundColor: '#7c3aed',
                                    color: 'white',
                                    padding: '2px 6px',
                                    borderRadius: '4px',
                                    fontSize: '10px',
                                    fontWeight: 600,
                                  }}
                                >
                                  Owner
                                </span>
                              )}
                              {user.isAdmin && (
                                <span
                                  style={{
                                    backgroundColor: '#dc2626',
                                    color: 'white',
                                    padding: '2px 6px',
                                    borderRadius: '4px',
                                    fontSize: '10px',
                                    fontWeight: 600,
                                  }}
                                >
                                  Admin
                                </span>
                              )}
                              {user.isLeader && (
                                <span
                                  style={{
                                    backgroundColor: '#059669',
                                    color: 'white',
                                    padding: '2px 6px',
                                    borderRadius: '4px',
                                    fontSize: '10px',
                                    fontWeight: 600,
                                  }}
                                >
                                  Leader
                                </span>
                              )}
                            </div>
                            <div style={{ fontSize: '12px', color: '#6b7280' }}>{user.email}</div>
                            {(user.departmentName || user.organizationName) && (
                              <div
                                style={{ fontSize: '11px', color: '#9ca3af', fontStyle: 'italic' }}
                              >
                                {user.departmentName && `${user.departmentName} • `}
                                {user.organizationName}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </>
              )}

              {!recipientsState.search.isLoading &&
                recipientsState.search.results.length === 0 &&
                (recipientsState.search.filters.organizationId ||
                  recipientsState.search.filters.departmentId ||
                  recipientsState.search.filters.name.trim()) && (
                  <div style={{ textAlign: 'center', padding: '20px', color: '#6b7280' }}>
                    No users found matching your search criteria.
                  </div>
                )}
            </div>
        </SearchCollapseContent>
      </SearchCollapseContainer>
    </RecipientsContainer>
  );
}
