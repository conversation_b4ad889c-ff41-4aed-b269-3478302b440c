'use client';

import React, { useState, useEffect, useCallback } from 'react';
import styled from 'styled-components';
import { X, User, Award, Send } from 'lucide-react';
import { appTheme } from '@/app/theme';
import { feedbackApi } from '@/services/feedbackService';
import DynamicRecipientsField from './DynamicRecipientsField';

const ModalOverlay = styled.div<{ $isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: ${props => props.$isOpen ? 'flex' : 'none'};
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: ${appTheme.spacing.lg};
`;

const ModalContainer = styled.div`
  background: ${appTheme.colors.background.main};
  border-radius: ${appTheme.borderRadius.lg};
  box-shadow: ${appTheme.shadows.lg};
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const ModalHeader = styled.div`
  padding: ${appTheme.spacing.lg};
  border-bottom: 1px solid ${appTheme.colors.border};
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const ModalTitle = styled.h2`
  font-size: ${appTheme.typography.fontSizes.xl};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: ${appTheme.colors.text.primary};
  margin: 0;
`;

const CloseButton = styled.button`
  padding: ${appTheme.spacing.sm};
  background: none;
  border: none;
  cursor: pointer;
  color: ${appTheme.colors.text.secondary};
  border-radius: ${appTheme.borderRadius.md};
  transition: ${appTheme.transitions.default};

  &:hover {
    background: ${appTheme.colors.background.lighter};
    color: ${appTheme.colors.text.primary};
  }
`;

const ModalContent = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: ${appTheme.spacing.lg};
`;

const FormGroup = styled.div`
  margin-bottom: ${appTheme.spacing.lg};
`;

const Label = styled.label`
  display: block;
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  color: ${appTheme.colors.text.primary};
  margin-bottom: ${appTheme.spacing.sm};
`;

const Select = styled.select`
  width: 100%;
  padding: ${appTheme.spacing.md};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.base};
  background: ${appTheme.colors.background.main};
  color: ${appTheme.colors.text.primary};

  &:focus {
    outline: none;
    border-color: ${appTheme.colors.primary};
    box-shadow: 0 0 0 2px ${appTheme.colors.primaryLight};
  }
`;

const Textarea = styled.textarea`
  width: 100%;
  min-height: 100px;
  padding: ${appTheme.spacing.md};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.base};
  font-family: inherit;
  resize: vertical;
  background: ${appTheme.colors.background.main};
  color: ${appTheme.colors.text.primary};

  &:focus {
    outline: none;
    border-color: ${appTheme.colors.primary};
    box-shadow: 0 0 0 2px ${appTheme.colors.primaryLight};
  }
`;

const Input = styled.input`
  width: 100%;
  padding: ${appTheme.spacing.md};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.base};
  background: ${appTheme.colors.background.main};
  color: ${appTheme.colors.text.primary};

  &:focus {
    outline: none;
    border-color: ${appTheme.colors.primary};
    box-shadow: 0 0 0 2px ${appTheme.colors.primaryLight};
  }
`;



const ModalFooter = styled.div`
  padding: ${appTheme.spacing.lg};
  border-top: 1px solid ${appTheme.colors.border};
  display: flex;
  gap: ${appTheme.spacing.md};
  justify-content: flex-end;
`;

const Button = styled.button<{ $variant?: 'primary' | 'secondary' }>`
  padding: ${appTheme.spacing.md} ${appTheme.spacing.lg};
  border-radius: ${appTheme.borderRadius.md};
  border: 1px solid ${props => 
    props.$variant === 'primary' ? appTheme.colors.primary : appTheme.colors.border
  };
  background-color: ${props => 
    props.$variant === 'primary' ? appTheme.colors.primary : appTheme.colors.background.main
  };
  color: ${props => 
    props.$variant === 'primary' ? 'white' : appTheme.colors.text.secondary
  };
  font-size: ${appTheme.typography.fontSizes.base};
  font-weight: ${appTheme.typography.fontWeights.medium};
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
  transition: ${appTheme.transitions.default};

  &:hover {
    background-color: ${props => 
      props.$variant === 'primary' ? appTheme.colors.primaryHover : appTheme.colors.background.lighter
    };
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const ErrorMessage = styled.div`
  color: ${appTheme.colors.error.main};
  font-size: ${appTheme.typography.fontSizes.sm};
  margin-top: ${appTheme.spacing.sm};
`;

interface CreateFeedbackModalProps {
  isOpen: boolean;
  onClose: () => void;
  onFeedbackCreated: () => void;
  feedbackTypes: any[];
}



export default function CreateFeedbackModal({
  isOpen,
  onClose,
  onFeedbackCreated,
  feedbackTypes,
}: CreateFeedbackModalProps) {
  const [formData, setFormData] = useState({
    feedbackTypeId: '',
    situation: '',
    behavior: '',
    impact: '',
    actionable: '',
    appreciation: '',
    growthToken: 0,
    userIds: [] as number[],
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen) {
      // Reset form when modal opens
      setFormData({
        feedbackTypeId: '',
        situation: '',
        behavior: '',
        impact: '',
        actionable: '',
        appreciation: '',
        growthToken: 0,
        userIds: [],
      });
      setError('');
    }
  }, [isOpen]);

  const handleInputChange = useCallback((field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    setError('');
  }, []);

  const handleUserIdsChange = useCallback((userIds: number[]) => {
    console.log('=== PARENT handleUserIdsChange ===');
    console.log('Received userIds:', userIds);
    console.log('Current formData.userIds:', formData.userIds);
    setFormData(prev => ({
      ...prev,
      userIds,
    }));
    console.log('Parent state updated');
  }, [formData.userIds]);

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      setError('');

      // Validation
      if (!formData.feedbackTypeId) {
        setError('Please select a feedback type');
        return;
      }
      if (formData.userIds.length === 0) {
        setError('Please select at least one user');
        return;
      }
      if (!formData.situation && !formData.behavior && !formData.impact && 
          !formData.actionable && !formData.appreciation) {
        setError('Please provide at least one feedback section');
        return;
      }

      await feedbackApi.createFeedback({
        feedbackTypeId: parseInt(formData.feedbackTypeId),
        situation: formData.situation || undefined,
        behavior: formData.behavior || undefined,
        impact: formData.impact || undefined,
        actionable: formData.actionable || undefined,
        appreciation: formData.appreciation || undefined,
        growthToken: formData.growthToken || undefined,
        userIds: formData.userIds,
      });

      onFeedbackCreated();
    } catch (err) {
      console.error('Failed to create feedback:', err);
      setError(err instanceof Error ? err.message : 'Failed to create feedback');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <ModalOverlay $isOpen={isOpen} onClick={onClose}>
      <ModalContainer onClick={(e) => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>Create New Feedback</ModalTitle>
          <CloseButton onClick={onClose}>
            <X size={24} />
          </CloseButton>
        </ModalHeader>

        <ModalContent>
          <FormGroup>
            <Label>Feedback Type *</Label>
            <Select
              value={formData.feedbackTypeId}
              onChange={(e) => handleInputChange('feedbackTypeId', e.target.value)}
            >
              <option value="">Select feedback type</option>
              {feedbackTypes.map((type) => (
                <option key={type.id} value={type.id}>
                  {type.displayName || type.name}
                </option>
              ))}
            </Select>
          </FormGroup>

          <FormGroup>
            <Label>Recipients *</Label>
            <DynamicRecipientsField
              feedbackTypeId={formData.feedbackTypeId}
              selectedUserIds={formData.userIds}
              onUserIdsChange={handleUserIdsChange}
              feedbackTypes={feedbackTypes}
            />
          </FormGroup>

          <FormGroup>
            <Label>Situation</Label>
            <Textarea
              value={formData.situation}
              onChange={(e) => handleInputChange('situation', e.target.value)}
              placeholder="Describe the situation or context..."
            />
          </FormGroup>

          <FormGroup>
            <Label>Behavior</Label>
            <Textarea
              value={formData.behavior}
              onChange={(e) => handleInputChange('behavior', e.target.value)}
              placeholder="Describe the specific behavior observed..."
            />
          </FormGroup>

          <FormGroup>
            <Label>Impact</Label>
            <Textarea
              value={formData.impact}
              onChange={(e) => handleInputChange('impact', e.target.value)}
              placeholder="Explain the impact of the behavior..."
            />
          </FormGroup>

          <FormGroup>
            <Label>Actionable</Label>
            <Textarea
              value={formData.actionable}
              onChange={(e) => handleInputChange('actionable', e.target.value)}
              placeholder="Suggest actionable improvements..."
            />
          </FormGroup>

          <FormGroup>
            <Label>Appreciation</Label>
            <Textarea
              value={formData.appreciation}
              onChange={(e) => handleInputChange('appreciation', e.target.value)}
              placeholder="Express appreciation or positive feedback..."
            />
          </FormGroup>

          <FormGroup>
            <Label>Growth Token</Label>
            <Input
              type="number"
              min="0"
              value={formData.growthToken}
              onChange={(e) => handleInputChange('growthToken', parseInt(e.target.value) || 0)}
              placeholder="0"
            />
          </FormGroup>

          {error && <ErrorMessage>{error}</ErrorMessage>}
        </ModalContent>

        <ModalFooter>
          <Button onClick={onClose}>Cancel</Button>
          <Button
            $variant="primary"
            onClick={handleSubmit}
            disabled={isSubmitting}
          >
            <Send size={16} />
            {isSubmitting ? 'Creating...' : 'Create Feedback'}
          </Button>
        </ModalFooter>
      </ModalContainer>
    </ModalOverlay>
  );
}
